---
type: "manual"
---

### 15. Workflow: Adding a New Client Element to an Existing Module (e.g., a new UI component or form field)

This workflow details the process of extending an existing client module with new UI components or specific functionalities, leveraging **shadcn/ui** where appropriate.

* **15.1. Discovery & Analysis:**
    * **Task:** Understand the purpose and specific requirements of the new client element. Analyze its impact on the existing module's UI, state, and interaction patterns, considering accessibility and responsiveness.
    * **Guidance for AI:** Identify the precise location within the existing client module (e.g., within a specific React component) for the new element. Ensure consistency with the module's current design system and user experience.
* **15.2. Task Planning:**
    * **Task:** Plan the element's addition, breaking it down into manageable units. Ensure the plan minimizes disruption to existing UI functionalities and adheres to the module's established patterns and design system.
    * **Guidance for AI:** Update the relevant "Task Planning Template" to incorporate the new element's UI implementation, state integration (React Query or Zustand), testing, and documentation.
* **15.3. Implementation:**
    * **Task:** Implement the new client element, extending existing React component patterns, utilizing **Tailwind CSS**, and maintaining "complete type safety" (**TypeScript**). Leverage **shadcn/ui** for base components where applicable.
    * **Guidance for AI:** Generate code for the new UI component or logic. Ensure "Zero Tolerance Policies" for warnings/errors are strictly met. Apply **Advanced Component Composition Patterns** like Render Props or Compound Components if the element requires highly flexible or implicitly contextual behavior.
* **15.4. Verification:**
    * **Task:** Update existing tests and add new tests specifically for the new element using **Vitest**/React Testing Library. Conduct targeted **Playwright** tests for UI interaction. Maintain 100% code coverage for the changes. Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Generate new test cases and execute the module's client-side test suite. Verify that all tests pass and that code coverage for the modified areas is 100%.
* **15.5. Documentation & Handover:**
    * **Task:** Update module-specific documentation within the "Frontend Specification" and prepare for AI agent transfer.
    * **Guidance for AI:** Generate or update the relevant sections within the module's frontend documentation, describing the new element's functionality, props, and usage using **JSDoc/TypeDoc**.