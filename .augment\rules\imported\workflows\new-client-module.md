---
type: "manual"
---

### 14. Workflow: Implementing a New Client Module (e.g., a new UI section or core feature component)

This workflow guides the AI Agent through creating and integrating a new, significant client-side module or feature section, such as a new dashboard view or a complex interactive form, adhering to **Domain-Driven Design (DDD)** principles for `src/modules`.

* **14.1. Discovery & Analysis:**
    * **Task:** Comprehensively understand the new module's purpose, define its clear scope (e.g., UI components, state management, data fetching logic), and analyze its required interactions with the backend API and other frontend modules.
    * **Guidance for AI:** Review mockups/wireframes and functional specifications. Determine the appropriate React component structure, state management needs (Zustand for client state, React Query for server state), and data fetching strategy. Identify necessary API endpoints, ensuring type safety via **OpenAPI Generator**-derived clients. Map components to the **Atomic Design Methodology** hierarchy.
* **14.2. Task Planning:**
    * **Task:** Break down the module implementation into smaller, manageable sub-tasks using the "Task Planning Template". This includes designing UI components, setting up state management, defining data fetching logic, and routing.
    * **Guidance for AI:** Generate a detailed task list. Ensure the plan aligns with Next.js best practices for server/client components, component reusability, and accessibility standards. Leverage "Code Generation" tools like **Hygen** or **Plop** to scaffold new components, hooks, or contexts.
* **14.3. Implementation:**
    * **Task:** Develop the client module's components strictly adhering to React/Next.js best practices, **SOLID principles** (for component design), **DRY**, **KISS**, "complete type safety" (**TypeScript**), and utilizing **Tailwind CSS** for styling, with **CSS Modules** for complex cases. Integrate with **React Query** for server state and **Zustand** for global/local UI state, distinguishing clearly between them.
    * **Guidance for AI:** Generate React components (JSX/TSX), Tailwind CSS classes, Zustand stores, and React Query hooks. Ensure "Zero Tolerance Policies" for warnings/errors from **ESLint** and **Prettier** are met, utilizing pre-commit hooks. Implement **immutability practices** for state management. Use "Unified Patterns" for data access and error handling.
* **14.4. Verification:**
    * **Task:** Conduct comprehensive testing, including unit tests (**Vitest**/React Testing Library), integration tests, and end-to-end tests (**Playwright**), ensuring "100% code coverage for new implementations". Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Generate test cases. Execute `npm run test` (Vitest/React Testing Library) and `npx playwright test`. Use **MSW (Mock Service Worker)** for API mocking. Verify "100% test pass rates" and analyze coverage reports. Perform visual regression testing if applicable. Ensure adherence to **Core Web Vitals** through performance analysis.
* **14.5. Documentation & Handover:**
    * **Task:** Update the "Frontend Specification" and "Developer Handbook" with details of the new client module. Prepare a comprehensive handover package for future development and AI agent transfer.
    * **Guidance for AI:** Generate or update documentation for new components, hooks, and state management logic using **JSDoc/TypeDoc**. Ensure clear usage examples and API definitions for components. Update `README.md` if the module significantly impacts project overview.