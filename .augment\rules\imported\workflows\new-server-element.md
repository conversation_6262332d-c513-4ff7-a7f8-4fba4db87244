---
type: "manual"
---

### 2. Workflow: Adding a New Element to an Existing Module

This workflow details the process of extending an existing module with new functionality or components.

* **2.1. Discovery & Analysis:**
    * **Task:** Understand the purpose and specific requirements of the new element. Analyze its impact on the existing module's structure, dependencies, and overall functionality.
    * **Guidance for AI:** Identify the precise location within the existing module for the new element. Ensure consistency with the module's current design patterns.
* **2.2. Task Planning:**
    * **Task:** Plan the element's addition, breaking it down into manageable units. Ensure the plan minimizes disruption to existing functionalities and adheres to the module's established patterns.
    * **Guidance for AI:** Update the relevant "Task Planning Template" to incorporate the new element's implementation, testing, and documentation.
* **2.3. Implementation:**
    * **Task:** Implement the new element, extending existing unified patterns and maintaining complete type safety (MyPy validation).
    * **Guidance for AI:** Generate code for the new element. Ensure "Zero Tolerance Policies" for warnings/errors are strictly met.
* **2.4. Verification:**
    * **Task:** Update existing tests and add new tests specifically for the new element, maintaining 100% code coverage for the changes. Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Generate new test cases and execute the module's test suite. Verify that all tests pass and that code coverage for the modified areas is 100%.
* **2.5. Documentation & Handover:**
    * **Task:** Update module-specific documentation and prepare for AI agent transfer.
    * **Guidance for AI:** Generate or update the relevant sections within the module's documentation, describing the new element's functionality and usage.