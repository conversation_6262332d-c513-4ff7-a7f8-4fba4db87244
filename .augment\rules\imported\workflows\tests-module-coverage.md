---
type: "manual"
---

### 7. Workflow: Increasing Test Coverage for a Specific Module

This workflow aims to improve the test coverage of a designated module, whether backend or frontend, directly supporting the project's "Target 100% for new implementations" and "extensive test coverage (≥85%)" metrics.

* **7.1. Discovery & Analysis:**
    * **Task:** Identify the specific module (backend Python or frontend TypeScript) and pinpoint areas within it with low test coverage using test coverage reports.
    * **Guidance for AI:** Analyze `pytest --cov=src --cov-report=html` output for Python backend or `npm run test -- --coverage` for frontend to highlight uncovered lines or branches.
* **7.2. Task Planning:**
    * **Task:** Plan the creation of new tests (unit, integration, E2E) to cover the identified uncovered code paths and functionalities, specific to the module's technology.
    * **Guidance for AI:** Create a detailed test plan, specifying the types of tests needed and the functionalities to be covered for the given module (e.g., backend API tests, frontend component tests, Playwright E2E scenarios).
* **7.3. Implementation:**
    * **Task:** Write new tests using **Pytest** for the backend or **Vitest/React Testing Library** for frontend unit/integration tests, and **Playwright** for frontend E2E tests. Ensure these tests adhere to best practices for testability and maintainability.
    * **Guidance for AI:** Generate test code that effectively exercises the uncovered parts of the module. For frontend, ensure tests account for state management (**React Query/Zustand**) and component interactions. Use **MSW** for frontend API mocking.
* **7.4. Verification:**
    * **Task:** Run the newly added tests along with the existing test suite for the module. Verify that the test coverage has increased as planned and that all tests pass.
    * **Guidance for AI:** Execute `poetry run pytest --cov=src` for backend or `npm run test` / `npx playwright test` for frontend and review the coverage report to confirm improvement towards the target.
* **7.5. Documentation & Handover:**
    * **Task:** Document the newly added tests, including their purpose and the specific functionalities they cover.
    * **Guidance for AI:** Update test documentation, "Developer Handbook", or inline code comments to reflect the expanded test suite.