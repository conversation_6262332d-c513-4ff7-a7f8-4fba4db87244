---
type: "manual"
---

### 17. Workflow: Updating an Existing Client Element (e.g., changing styling or behavior of a button)

This workflow addresses the process of modifying a specific UI component or its behavior within an existing client module, emphasizing **Props-Based Customization** and **Accessibility**.

* **17.1. Discovery & Analysis:**
    * **Task:** Pinpoint the specific client element within the module that needs updating. Clearly understand its current behavior, styling, and the precise scope of the required modification.
    * **Guidance for AI:** Review the element's existing component code, styling definitions (**Tailwind CSS**), and usage context to identify dependencies and potential side effects of the update. Consider implications for "Responsiveness" and "Accessibility".
* **17.2. Task Planning:**
    * **Task:** Plan the update focusing on isolated changes to minimize side effects and potential UI regressions.
    * **Guidance for AI:** Create a mini-plan for the element, detailing modifications, required tests, and documentation updates.
* **17.3. Implementation:**
    * **Task:** Modify the element's code (JSX/TSX), styling (**Tailwind CSS**), or associated logic, maintaining existing design principles, accessibility, and "complete type safety" (**TypeScript**). Leverage **Props-Based Customization** where appropriate.
    * **Guidance for AI:** Generate code changes for the specific client element. Ensure no new warnings or errors are introduced from ESLint/Prettier.
* **17.4. Verification:**
    * **Task:** Conduct targeted testing on the updated client element using **Vitest**/React Testing Library for unit tests and **Playwright** for functional/visual regression tests. Ensure no regressions are introduced.
    * **Guidance for AI:** Execute specific unit tests related to the element. Confirm "100% test pass rates" and no degradation in "Code Quality".
* **17.5. Documentation & Handover:**
    * **Task:** Update specific element documentation within the "Frontend Specification" to reflect the changes.
    * **Guidance for AI:** Generate or update documentation detailing the modifications to the client element's behavior, props, or styling using **JSDoc/TypeDoc**.