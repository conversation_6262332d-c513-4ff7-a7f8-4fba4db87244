---
type: "manual"
---

### 16. Workflow: Updating an Existing Client Module (e.g., refactoring a major UI section)

This workflow focuses on modifying or enhancing an already implemented client module, prioritizing "Performance Optimization".

* **16.1. Discovery & Analysis:**
    * **Task:** Analyze the current functionality and UI of the client module and identify specific areas that require updates, redesign, or refinement. Consider any internal (state, components) or external (API interactions) dependencies affected by the update.
    * **Guidance for AI:** Review existing module code, design specifications, and user feedback. Identify potential impacts on user experience and performance. Analyze bundle size using tools like `next-bundle-analyzer` if performance is a concern.
* **16.2. Task Planning:**
    * **Task:** Break down the update tasks into smaller units, prioritizing non-breaking UI changes, performance improvements, or bug fixes. Utilize the "Task Planning Template".
    * **Guidance for AI:** Create a detailed plan for modifying the client module, ensuring adherence to the "5-Phase Methodology" and considering accessibility and responsiveness. Plan for **Memoization** (React.memo, useMemo, useCallback) or **Virtualization** for performance.
* **16.3. Implementation:**
    * **Task:** Apply the necessary updates to the client module, ensuring continued adherence to component best practices, "unified patterns" (for data fetching/state), Tailwind CSS, and "complete type safety" (**TypeScript**). Address any identified "technical debt" in the frontend.
    * **Guidance for AI:** Generate code modifications for React components, styles, state logic, or data fetching. Ensure all "Zero Tolerance Policies" for warnings, errors, and technical debt are adhered to. Focus on "Pure Functions" for utilities.
* **16.4. Verification:**
    * **Task:** Rerun all relevant client-side tests for the module (unit, integration, E2E) and add new tests for updated functionalities. Ensure zero warnings/errors are introduced and UI regressions are prevented. Utilize the "Quality Assurance Checklist".
    * **Guidance for AI:** Execute **Vitest**/React Testing Library and **Playwright** tests for the module. Verify "100% test pass rates" and analyze code quality metrics. Perform visual regression tests. Conduct performance audits to ensure **Core Web Vitals** are optimized.
* **16.5. Documentation & Handover:**
    * **Task:** Update the client module's documentation within the "Frontend Specification" and any relevant user-facing guides.
    * **Guidance for AI:** Generate or update documentation reflecting the changes and improvements made to the client module's UI, state, and interactions.