---
type: "manual"
---

### 9. Workflow: Enhancing an Existing Feature

This workflow outlines the process for improving or extending an already implemented feature, involving both backend and frontend components.

* **9.1. Discovery & Analysis:**
    * **Task:** Evaluate the current feature's backend logic and frontend UI/UX. Understand its existing functionality and identify the precise scope of the enhancement. Analyze potential dependencies and impacts on other system parts across both layers.
    * **Guidance for AI:** Review existing feature documentation, backend code, frontend code, user feedback, and performance metrics to define the enhancement requirements clearly.
* **9.2. Task Planning:**
    * **Task:** Plan the enhancements, breaking them into manageable steps for both backend and frontend. Prioritize changes that ensure backward compatibility and improve performance.
    * **Guidance for AI:** Update the "Task Planning Template" to detail the enhancement implementation, testing, and documentation for both backend services and frontend components.
* **9.3. Implementation:**
    * **Task:** Implement the enhancements across relevant backend services and frontend components, strictly adhering to established coding standards, "unified patterns", "complete type safety", and "SOLID" principles.
    * **Guidance for AI:** Modify existing code or add new components as required in both backend (Python) and frontend (TypeScript/React). Ensure "Zero Tolerance Policies" for warnings/errors and technical debt are maintained across the full stack.
* **9.4. Verification:**
    * **Task:** Conduct extensive regression testing to ensure the existing functionality remains intact across backend APIs and frontend UI. Add new tests for the enhanced functionalities, contributing to 100% coverage for changes. Ensure zero security vulnerabilities are introduced.
    * **Guidance for AI:** Execute the feature's existing test suite and newly generated tests for both backend (**pytest**) and frontend (**Vitest/React Testing Library/Playwright**). Verify "100% test pass rates" and perform security scans if applicable. Use the "Quality Assurance Checklist". Conduct frontend performance audits.
* **9.5. Documentation & Handover:**
    * **Task:** Update the feature's documentation (both backend and frontend parts) and any relevant user guides to reflect the enhancements.
    * **Guidance for AI:** Generate or update documentation, ensuring clarity on new functionalities and changes to existing ones across the entire feature.