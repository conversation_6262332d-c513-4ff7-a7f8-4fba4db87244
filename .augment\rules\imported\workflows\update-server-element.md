---
type: "manual"
---

### 4. Workflow: Updating an Existing Element

This workflow addresses the process of modifying a specific component or feature within an existing module.

* **4.1. Discovery & Analysis:**
    * **Task:** Pinpoint the specific element within the module that needs updating. Clearly understand its current role and the precise scope of the required modification.
    * **Guidance for AI:** Review the element's existing code and documentation to identify dependencies and potential side effects of the update.
* **4.2. Task Planning:**
    * **Task:** Plan the update focusing on isolated changes to minimize side effects and potential regressions.
    * **Guidance for AI:** Create a mini-plan for the element, detailing modifications, required tests, and documentation updates.
* **4.3. Implementation:**
    * **Task:** Modify the element's code, maintaining existing design principles, "unified patterns", and full type safety.
    * **Guidance for AI:** Generate code changes for the specific element. Ensure no new warnings or errors are introduced.
* **4.4. Verification:**
    * **Task:** Conduct targeted testing on the updated element and relevant components within its module. Ensure no regressions are introduced.
    * **Guidance for AI:** Execute specific unit tests related to the element. Confirm "100% test pass rates" and no degradation in "Code Quality".
* **4.5. Documentation & Handover:**
    * **Task:** Update specific element documentation within the module to reflect the changes.
    * **Guidance for AI:** Generate or update documentation detailing the modifications to the element.