{"extends": ["next/core-web-vitals", "@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-const": "error", "@typescript-eslint/no-inferrable-types": "error", "prefer-const": "error", "no-var": "error", "no-console": "warn", "eqeqeq": "error", "curly": "error"}, "overrides": [{"files": ["**/*.test.ts", "**/*.test.tsx", "**/*.spec.ts", "**/*.spec.tsx"], "env": {"jest": true}, "rules": {"@typescript-eslint/no-explicit-any": "off"}}]}