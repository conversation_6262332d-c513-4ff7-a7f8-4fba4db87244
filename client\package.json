{"name": "ultimate-electrical-designer-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"next": "15.3.0", "react": "^18", "react-dom": "^18", "@tanstack/react-query": "^5.0.0", "@tanstack/react-query-devtools": "^5.0.0", "zustand": "^4.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.3.0", "tailwindcss": "^4.1.0", "postcss": "^8", "autoprefixer": "^10.0.1", "vitest": "^1.0.0", "@vitejs/plugin-react": "^4.0.0", "@testing-library/react": "^14.0.0", "@testing-library/jest-dom": "^6.0.0", "@testing-library/user-event": "^14.0.0", "jsdom": "^23.0.0", "@playwright/test": "^1.40.0", "prettier": "^3.0.0", "prettier-plugin-tailwindcss": "^0.5.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0"}}