'use client'

import { useEffect, ReactNode } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'

interface RouteGuardProps {
  children: ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
  redirectTo?: string
}

export function RouteGuard({ 
  children, 
  requireAuth = false, 
  requireAdmin = false,
  redirectTo 
}: RouteGuardProps) {
  const router = useRouter()
  const { isAuthenticated, isAdmin, isLoading, user } = useAuth()

  useEffect(() => {
    // Don't redirect while loading
    if (isLoading) return

    // Check authentication requirement
    if (requireAuth && !isAuthenticated) {
      router.push(redirectTo || '/login')
      return
    }

    // Check admin requirement
    if (requireAdmin && (!isAuthenticated || !isAdmin())) {
      router.push(redirectTo || '/dashboard')
      return
    }

    // Redirect authenticated users away from auth pages
    if (!requireAuth && !requireAdmin && isAuthenticated) {
      if (window.location.pathname === '/login') {
        router.push('/dashboard')
        return
      }
    }
  }, [isAuthenticated, isAdmin, isLoading, requireAuth, requireAdmin, redirectTo, router])

  // Show loading spinner while checking auth
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  // Don't render if auth requirements aren't met
  if (requireAuth && !isAuthenticated) {
    return null
  }

  if (requireAdmin && (!isAuthenticated || !isAdmin())) {
    return null
  }

  // Don't render auth pages if already authenticated
  if (!requireAuth && !requireAdmin && isAuthenticated && window.location.pathname === '/login') {
    return null
  }

  return <>{children}</>
}

// Higher-order component for protecting pages
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: { requireAdmin?: boolean; redirectTo?: string } = {}
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <RouteGuard 
        requireAuth={true} 
        requireAdmin={options.requireAdmin}
        redirectTo={options.redirectTo}
      >
        <Component {...props} />
      </RouteGuard>
    )
  }
}

// Higher-order component for admin-only pages
export function withAdmin<P extends object>(
  Component: React.ComponentType<P>,
  options: { redirectTo?: string } = {}
) {
  return function AdminComponent(props: P) {
    return (
      <RouteGuard 
        requireAuth={true} 
        requireAdmin={true}
        redirectTo={options.redirectTo}
      >
        <Component {...props} />
      </RouteGuard>
    )
  }
}
