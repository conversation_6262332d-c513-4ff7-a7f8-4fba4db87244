import { describe, it, expect, beforeEach } from 'vitest'
import { renderHook, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { useAuth } from '../useAuth'
import { useAuthStore } from '@/stores/authStore'
import { mockUser, mockLoginResponse, mockFetch } from '@/test/utils'

// Mock the API client
vi.mock('@/lib/api/client', () => ({
  apiClient: {
    login: vi.fn(),
    logout: vi.fn(),
    getCurrentUser: vi.fn(),
    setAuthToken: vi.fn(),
    clearAuthToken: vi.fn(),
  },
}))

// Mock the token manager
vi.mock('@/lib/auth/tokenManager', () => ({
  TokenManager: {
    setAccessToken: vi.fn(),
    clearTokens: vi.fn(),
    initializeTokens: vi.fn(),
  },
}))

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('useAuth Hook', () => {
  beforeEach(() => {
    // Reset auth store
    useAuthStore.getState().clearAuth()
    vi.clearAllMocks()
  })

  it('initializes with default state', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    })

    expect(result.current.user).toBeNull()
    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.isLoading).toBe(false)
  })

  it('handles successful login', async () => {
    const { apiClient } = await import('@/lib/api/client')
    vi.mocked(apiClient.login).mockResolvedValue(mockLoginResponse)

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    })

    await waitFor(async () => {
      await result.current.login({
        username: '<EMAIL>',
        password: 'password123',
      })
    })

    expect(result.current.isAuthenticated).toBe(true)
    expect(result.current.user).toEqual(mockUser)
  })

  it('handles login failure', async () => {
    const { apiClient } = await import('@/lib/api/client')
    const error = new Error('Invalid credentials')
    vi.mocked(apiClient.login).mockRejectedValue(error)

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    })

    await expect(
      result.current.login({
        username: '<EMAIL>',
        password: 'wrongpassword',
      })
    ).rejects.toThrow('Invalid credentials')

    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.user).toBeNull()
  })

  it('handles logout', async () => {
    const { apiClient } = await import('@/lib/api/client')
    vi.mocked(apiClient.logout).mockResolvedValue({
      message: 'Logged out successfully',
      logged_out_at: '2024-01-01T00:00:00Z',
    })

    // First set authenticated state
    useAuthStore.getState().setAuth(mockUser, 'mock-token')

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    })

    expect(result.current.isAuthenticated).toBe(true)

    await waitFor(async () => {
      await result.current.logout()
    })

    expect(result.current.isAuthenticated).toBe(false)
    expect(result.current.user).toBeNull()
  })

  it('checks user roles correctly', () => {
    useAuthStore.getState().setAuth(mockUser, 'mock-token')

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    })

    expect(result.current.hasRole('VIEWER')).toBe(true)
    expect(result.current.hasRole('ADMIN')).toBe(false)
    expect(result.current.isAdmin()).toBe(false)
  })

  it('checks admin role correctly', () => {
    const adminUser = { ...mockUser, role: 'ADMIN' as const, is_admin: true }
    useAuthStore.getState().setAuth(adminUser, 'mock-token')

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    })

    expect(result.current.isAdmin()).toBe(true)
    expect(result.current.hasRole('ADMIN')).toBe(true)
  })
})
